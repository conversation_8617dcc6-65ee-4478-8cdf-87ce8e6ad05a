# 昆仑问策系统Linux部署优化指南

## 1. 系统环境推荐

### 推荐Linux发行版
- **Ubuntu 22.04 LTS** (推荐) - 稳定性好，社区支持强
- **CentOS Stream 9** - 企业级稳定性
- **Rocky Linux 9** - RHEL兼容，免费企业级

### 硬件配置建议
```bash
# 最低配置
CPU: 4核心
内存: 8GB
存储: 100GB SSD
网络: 1Gbps

# 推荐配置 (支持500+并发)
CPU: 8核心 (Intel Xeon或AMD EPYC)
内存: 16GB
存储: 200GB NVMe SSD
网络: 10Gbps
```

## 2. 系统优化配置

### 内核参数优化
```bash
# /etc/sysctl.conf
# 网络优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000

# 内存优化
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5

# 文件描述符限制
fs.file-max = 2097152
fs.nr_open = 2097152

# 应用生效
sysctl -p
```

### 用户限制配置
```bash
# /etc/security/limits.conf
kunlun soft nofile 65535
kunlun hard nofile 65535
kunlun soft nproc 32768
kunlun hard nproc 32768
```

## 3. Web服务器配置

### Nginx反向代理配置
```nginx
# /etc/nginx/sites-available/kunlun
upstream kunlun_app {
    # 多进程负载均衡
    server 127.0.0.1:5000 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:5001 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:5002 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:5003 weight=1 max_fails=3 fail_timeout=30s;
    
    # 保持连接
    keepalive 32;
}

server {
    listen 80;
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /etc/ssl/certs/kunlun.crt;
    ssl_certificate_key /etc/ssl/private/kunlun.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # 性能优化
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    keepalive_timeout 65s;
    send_timeout 60s;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript 
               application/javascript application/json application/xml+rss;
    
    # 静态文件缓存
    location /static/ {
        alias /opt/kunlun/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
    }
    
    # WebSocket支持
    location /socket.io/ {
        proxy_pass http://kunlun_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }
    
    # 流式API支持
    location /chat/stream {
        proxy_pass http://kunlun_app;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 流式响应优化
        proxy_buffering off;
        proxy_cache off;
        proxy_read_timeout 300s;
        proxy_connect_timeout 10s;
        proxy_send_timeout 300s;
    }
    
    # 主应用代理
    location / {
        proxy_pass http://kunlun_app;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
}
```

## 4. 数据库优化

### KingbaseES配置优化
```bash
# /opt/Kingbase/ES/V8/data/kingbase.conf

# 内存配置
shared_buffers = 2GB                    # 25%的系统内存
effective_cache_size = 6GB              # 75%的系统内存
work_mem = 64MB                         # 每个查询操作的内存
maintenance_work_mem = 512MB            # 维护操作内存

# 连接配置
max_connections = 200                   # 最大连接数
superuser_reserved_connections = 3      # 超级用户保留连接

# 检查点配置
checkpoint_completion_target = 0.9      # 检查点完成目标
wal_buffers = 16MB                      # WAL缓冲区
checkpoint_timeout = 10min              # 检查点超时

# 日志配置
log_destination = 'csvlog'              # CSV格式日志
logging_collector = on                  # 启用日志收集
log_directory = 'log'                   # 日志目录
log_filename = 'kingbase-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d                   # 日志轮转时间
log_rotation_size = 100MB               # 日志轮转大小

# 性能监控
log_min_duration_statement = 1000       # 记录慢查询(1秒)
log_checkpoints = on                    # 记录检查点
log_connections = on                    # 记录连接
log_disconnections = on                 # 记录断开连接
log_lock_waits = on                     # 记录锁等待

# 并发优化
max_worker_processes = 8                # 最大工作进程
max_parallel_workers = 4                # 最大并行工作进程
max_parallel_workers_per_gather = 2     # 每个Gather节点的并行工作进程
```

### 数据库连接池优化
```python
# 更新database.py中的连接池配置
DB_POOL_CONFIG = {
    'minconn': 10,          # 最小连接数
    'maxconn': 50,          # 最大连接数 (增加到50)
    'host': os.getenv('KINGBASE_HOST'),
    'port': int(os.getenv('KINGBASE_PORT', 54321)),
    'user': os.getenv('KINGBASE_USER'),
    'password': os.getenv('KINGBASE_PASSWORD'),
    'database': os.getenv('KINGBASE_DATABASE'),
    'options': f"-c search_path={os.getenv('KINGBASE_SCHEMA')}",
    'client_encoding': 'UTF8',
    'connect_timeout': 10,
    'application_name': 'kunlun_chat_system'
}
```

## 5. Redis缓存优化

### Redis配置优化
```bash
# /etc/redis/redis.conf

# 内存配置
maxmemory 4gb                           # 最大内存使用
maxmemory-policy allkeys-lru            # 内存淘汰策略

# 持久化配置
save 900 1                              # 900秒内至少1个key变化时保存
save 300 10                             # 300秒内至少10个key变化时保存
save 60 10000                           # 60秒内至少10000个key变化时保存

# 网络配置
tcp-keepalive 300                       # TCP keepalive
timeout 0                               # 客户端空闲超时(0=禁用)
tcp-backlog 511                         # TCP监听队列长度

# 性能优化
hash-max-ziplist-entries 512            # 哈希表压缩列表最大条目
hash-max-ziplist-value 64               # 哈希表压缩列表最大值
list-max-ziplist-size -2                # 列表压缩配置
set-max-intset-entries 512              # 整数集合最大条目

# 日志配置
loglevel notice                         # 日志级别
logfile /var/log/redis/redis-server.log # 日志文件
syslog-enabled yes                      # 启用系统日志

# 安全配置
requirepass your_redis_password         # Redis密码
rename-command FLUSHDB ""               # 禁用危险命令
rename-command FLUSHALL ""
rename-command CONFIG ""
```

### Redis集群配置(高可用)
```bash
# Redis Sentinel配置 - /etc/redis/sentinel.conf
port 26379
sentinel monitor kunlun-redis 127.0.0.1 6379 2
sentinel auth-pass kunlun-redis your_redis_password
sentinel down-after-milliseconds kunlun-redis 30000
sentinel parallel-syncs kunlun-redis 1
sentinel failover-timeout kunlun-redis 180000
```

## 6. 应用层优化

### Gunicorn WSGI服务器配置
```python
# gunicorn_config.py
import multiprocessing
import os

# 服务器配置
bind = "127.0.0.1:5000"
backlog = 2048

# 工作进程配置
workers = multiprocessing.cpu_count() * 2 + 1  # 推荐公式
worker_class = "eventlet"  # 支持WebSocket
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 50
preload_app = True
timeout = 120
keepalive = 5

# 日志配置
accesslog = "/var/log/kunlun/gunicorn_access.log"
errorlog = "/var/log/kunlun/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程管理
pidfile = "/var/run/kunlun/gunicorn.pid"
user = "kunlun"
group = "kunlun"
tmp_upload_dir = None

# 性能优化
worker_tmp_dir = "/dev/shm"  # 使用内存文件系统
```

### 多进程部署脚本
```bash
#!/bin/bash
# /opt/kunlun/scripts/start_cluster.sh

# 环境变量
export PYTHONPATH="/opt/kunlun:$PYTHONPATH"
export FLASK_ENV="production"

# 启动多个应用实例
for port in 5000 5001 5002 5003; do
    echo "启动应用实例 - 端口: $port"
    cd /opt/kunlun
    gunicorn -c gunicorn_config.py \
        --bind 127.0.0.1:$port \
        --pid /var/run/kunlun/gunicorn_$port.pid \
        --access-logfile /var/log/kunlun/access_$port.log \
        --error-logfile /var/log/kunlun/error_$port.log \
        --daemon \
        app:app
done

echo "所有应用实例启动完成"
```

## 7. 系统监控配置

### Prometheus监控配置
```yaml
# /etc/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "kunlun_rules.yml"

scrape_configs:
  - job_name: 'kunlun-app'
    static_configs:
      - targets: ['localhost:5000', 'localhost:5001', 'localhost:5002', 'localhost:5003']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']

  - job_name: 'kingbase'
    static_configs:
      - targets: ['localhost:9187']

  - job_name: 'nginx'
    static_configs:
      - targets: ['localhost:9113']

  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']
```

### 系统监控脚本
```bash
#!/bin/bash
# /opt/kunlun/scripts/monitor.sh

# 检查应用健康状态
check_app_health() {
    for port in 5000 5001 5002 5003; do
        response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$port/health)
        if [ "$response" != "200" ]; then
            echo "应用实例 $port 不健康，尝试重启"
            systemctl restart kunlun@$port
        fi
    done
}

# 检查数据库连接
check_database() {
    python3 -c "
import psycopg2
import os
from dotenv import load_dotenv
load_dotenv()

try:
    conn = psycopg2.connect(
        host=os.getenv('KINGBASE_HOST'),
        port=os.getenv('KINGBASE_PORT'),
        user=os.getenv('KINGBASE_USER'),
        password=os.getenv('KINGBASE_PASSWORD'),
        database=os.getenv('KINGBASE_DATABASE')
    )
    conn.close()
    print('数据库连接正常')
except Exception as e:
    print(f'数据库连接失败: {e}')
    exit(1)
"
}

# 检查Redis连接
check_redis() {
    redis-cli -h localhost -p 6379 ping > /dev/null
    if [ $? -ne 0 ]; then
        echo "Redis连接失败，尝试重启"
        systemctl restart redis
    fi
}

# 执行检查
check_app_health
check_database
check_redis

echo "系统监控检查完成 - $(date)"
```

## 8. 安全配置

### 防火墙配置
```bash
# UFW防火墙配置
ufw --force reset
ufw default deny incoming
ufw default allow outgoing

# 允许SSH
ufw allow 22/tcp

# 允许HTTP/HTTPS
ufw allow 80/tcp
ufw allow 443/tcp

# 允许内部服务通信
ufw allow from 127.0.0.1 to any port 5000:5003
ufw allow from 127.0.0.1 to any port 6379
ufw allow from 127.0.0.1 to any port 54321

# 启用防火墙
ufw --force enable
```

### SSL证书自动更新
```bash
#!/bin/bash
# /opt/kunlun/scripts/renew_ssl.sh

# 使用Let's Encrypt自动更新SSL证书
certbot renew --quiet --nginx

# 重新加载Nginx配置
nginx -t && systemctl reload nginx

echo "SSL证书更新完成 - $(date)"
```

## 9. 部署自动化

### Docker容器化部署
```dockerfile
# Dockerfile
FROM python:3.13-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -m -s /bin/bash kunlun

# 设置工作目录
WORKDIR /opt/kunlun

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .
RUN chown -R kunlun:kunlun /opt/kunlun

# 切换到应用用户
USER kunlun

# 暴露端口
EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# 启动命令
CMD ["gunicorn", "-c", "gunicorn_config.py", "app:app"]
```

### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  kunlun-app:
    build: .
    ports:
      - "5000-5003:5000"
    environment:
      - FLASK_ENV=production
      - KINGBASE_HOST=kingbase
      - REDIS_HOST=redis
    depends_on:
      - kingbase
      - redis
    deploy:
      replicas: 4
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
    depends_on:
      - kunlun-app

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf

  kingbase:
    image: kingbase/kingbasees:v8
    environment:
      - KINGBASE_PASSWORD=your_password
    volumes:
      - kingbase_data:/opt/Kingbase/ES/V8/data
    ports:
      - "54321:54321"

volumes:
  redis_data:
  kingbase_data:
```
